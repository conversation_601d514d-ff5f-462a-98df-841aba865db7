"""Encoding utilities for handling Unicode characters safely."""

import sys
import logging


def safe_log_message(message: str) -> str:
    """
    Convert a log message to be safe for the current console encoding.
    
    This function replaces Unicode emoji characters with ASCII alternatives
    to prevent UnicodeEncodeError on Windows systems using CP1252 encoding.
    
    Args:
        message: The original log message that may contain Unicode characters
        
    Returns:
        A safe version of the message with ASCII alternatives
    """
    # Common emoji replacements for logging
    emoji_replacements = {
        '🔗': '[LINK]',
        '📐': '[VIEWPORT]',
        '🔄': '[LOADING]',
        '✅': '[SUCCESS]',
        '❌': '[ERROR]',
        '⚠️': '[WARNING]',
        '🚀': '[START]',
        '🛑': '[STOP]',
        '👋': '[EXIT]',
        '🎉': '[COMPLETE]',
        '🔍': '[SEARCH]',
        '📁': '[FOLDER]',
        '🧪': '[TEST]',
        '🤖': '[BOT]',
        '🛍️': '[SHOP]',
        '📋': '[PLAN]',
        '🚫': '[BLOCKED]',
        '🔧': '[TOOL]',
        '📊': '[STATS]',
        '🎯': '[TARGET]',
        '💡': '[INFO]',
        '🔥': '[HOT]',
        '⭐': '[STAR]',
        '🎨': '[STYLE]',
        '🌟': '[FEATURE]',
        '🚨': '[ALERT]',
        '📝': '[NOTE]',
        '🔒': '[SECURE]',
        '🌐': '[WEB]',
        '📈': '[GROWTH]',
        '⚡': '[FAST]',
        '🎪': '[EVENT]',
        '🏆': '[WIN]',
        '🎭': '[MASK]',
        '🎬': '[ACTION]',
        '🎵': '[SOUND]',
        '🎲': '[RANDOM]',
        '🎯': '[AIM]',
        '🎪': '[CIRCUS]',
        '🎨': '[ART]',
        '🎭': '[DRAMA]',
        '🎪': '[SHOW]'
    }
    
    # Replace emojis with ASCII alternatives
    safe_message = message
    for emoji, replacement in emoji_replacements.items():
        safe_message = safe_message.replace(emoji, replacement)
    
    return safe_message


def safe_print(message: str, **kwargs) -> None:
    """
    Print a message safely, handling encoding issues.
    
    Args:
        message: The message to print
        **kwargs: Additional arguments to pass to print()
    """
    try:
        print(message, **kwargs)
    except UnicodeEncodeError:
        # Fallback to safe message
        safe_message = safe_log_message(message)
        print(safe_message, **kwargs)


def configure_safe_logging() -> None:
    """
    Configure logging to handle Unicode characters safely.
    
    This function sets up logging with proper encoding and error handling
    to prevent UnicodeEncodeError issues on Windows systems.
    """
    import logging
    
    class SafeFormatter(logging.Formatter):
        """Custom formatter that handles Unicode characters safely."""
        
        def format(self, record):
            # Get the original formatted message
            original_message = super().format(record)
            
            # Make it safe for the current console encoding
            return safe_log_message(original_message)
    
    # Get the root logger
    root_logger = logging.getLogger()
    
    # Update all existing handlers to use safe formatting
    for handler in root_logger.handlers:
        if isinstance(handler, logging.StreamHandler):
            # Use safe formatter for console output
            handler.setFormatter(SafeFormatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            ))


def get_console_encoding() -> str:
    """
    Get the current console encoding.
    
    Returns:
        The encoding name (e.g., 'cp1252', 'utf-8')
    """
    try:
        return sys.stdout.encoding or 'utf-8'
    except AttributeError:
        return 'utf-8'


def is_unicode_safe() -> bool:
    """
    Check if the current console supports Unicode characters.
    
    Returns:
        True if Unicode is supported, False otherwise
    """
    encoding = get_console_encoding().lower()
    
    # These encodings generally support Unicode well
    unicode_safe_encodings = ['utf-8', 'utf-16', 'utf-32']
    
    return any(safe_enc in encoding for safe_enc in unicode_safe_encodings)


def setup_encoding_safety() -> None:
    """
    Set up encoding safety measures for the entire application.
    
    This function should be called early in the application startup
    to ensure all logging and console output is handled safely.
    """
    # Configure safe logging
    configure_safe_logging()
    
    # Log the current encoding status
    logger = logging.getLogger(__name__)
    encoding = get_console_encoding()
    unicode_safe = is_unicode_safe()
    
    logger.info(f"[ENCODING] Console encoding: {encoding}")
    logger.info(f"[ENCODING] Unicode safe: {unicode_safe}")
    
    if not unicode_safe:
        logger.warning("[ENCODING] Console does not fully support Unicode - using ASCII alternatives for emojis")
